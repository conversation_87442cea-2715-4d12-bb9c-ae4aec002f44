#Updated at 2023-02-08T12:38:17.180Z
#Wed Feb 08 20:38:17 CST 2023
hoodie.table.precombine.field=ts
hoodie.datasource.write.drop.partition.columns=false
hoodie.table.type=MERGE_ON_READ
hoodie.archivelog.folder=archived
hoodie.compaction.payload.class=org.apache.hudi.common.model.OverwriteWithLatestAvroPayload
hoodie.timeline.layout.version=1
hoodie.table.version=5
hoodie.table.metadata.partitions=files
hoodie.table.recordkey.fields=uuid
hoodie.database.name=zya
hoodie.datasource.write.partitionpath.urlencode=false
hoodie.table.keygenerator.class=org.apache.hudi.keygen.NonpartitionedKeyGenerator
hoodie.table.name=test_hudi_mor5
hoodie.datasource.write.hive_style_partitioning=true
hoodie.table.checksum=3597908696
hoodie.table.create.schema={"type"\:"record","name"\:"test_hudi_mor5_record","namespace"\:"hoodie.test_hudi_mor5","fields"\:[{"name"\:"_hoodie_commit_time","type"\:["string","null"]},{"name"\:"_hoodie_commit_seqno","type"\:["string","null"]},{"name"\:"_hoodie_record_key","type"\:["string","null"]},{"name"\:"_hoodie_partition_path","type"\:["string","null"]},{"name"\:"_hoodie_file_name","type"\:["string","null"]},{"name"\:"uuid","type"\:["string","null"]},{"name"\:"ts","type"\:["int","null"]},{"name"\:"a","type"\:["int","null"]},{"name"\:"b","type"\:["string","null"]},{"name"\:"c","type"\:[{"type"\:"long","logicalType"\:"timestamp-micros"},"null"]}]}

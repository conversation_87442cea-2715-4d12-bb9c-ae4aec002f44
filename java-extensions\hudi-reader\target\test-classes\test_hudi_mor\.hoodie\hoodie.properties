#Updated at 2023-01-05T06:29:40.500Z
#Thu Jan 05 14:29:40 CST 2023
hoodie.table.precombine.field=ts
hoodie.datasource.write.drop.partition.columns=false
hoodie.table.type=MERGE_ON_READ
hoodie.archivelog.folder=archived
hoodie.compaction.payload.class=org.apache.hudi.common.model.OverwriteWithLatestAvroPayload
hoodie.timeline.layout.version=1
hoodie.table.version=5
hoodie.table.metadata.partitions=files
hoodie.table.recordkey.fields=uuid
hoodie.database.name=zya
hoodie.datasource.write.partitionpath.urlencode=false
hoodie.table.keygenerator.class=org.apache.hudi.keygen.NonpartitionedKeyGenerator
hoodie.table.name=test_hudi_mor
hoodie.datasource.write.hive_style_partitioning=true
hoodie.table.checksum=2859679923
hoodie.table.create.schema={"type"\:"record","name"\:"test_hudi_mor_record","namespace"\:"hoodie.test_hudi_mor","fields"\:[{"name"\:"_hoodie_commit_time","type"\:["string","null"]},{"name"\:"_hoodie_commit_seqno","type"\:["string","null"]},{"name"\:"_hoodie_record_key","type"\:["string","null"]},{"name"\:"_hoodie_partition_path","type"\:["string","null"]},{"name"\:"_hoodie_file_name","type"\:["string","null"]},{"name"\:"uuid","type"\:["string","null"]},{"name"\:"ts","type"\:["int","null"]},{"name"\:"a","type"\:["int","null"]},{"name"\:"b","type"\:["string","null"]},{"name"\:"c","type"\:[{"type"\:"array","items"\:["int","null"]},"null"]},{"name"\:"d","type"\:[{"type"\:"map","values"\:["int","null"]},"null"]},{"name"\:"e","type"\:[{"type"\:"record","name"\:"e","namespace"\:"hoodie.test_hudi_mor.test_hudi_mor_record","fields"\:[{"name"\:"a","type"\:["int","null"]},{"name"\:"b","type"\:["string","null"]}]},"null"]}]}

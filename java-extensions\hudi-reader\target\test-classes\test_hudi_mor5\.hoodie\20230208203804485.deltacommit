{"partitionToWriteStats": {"": [{"fileId": "07eeba07-b04d-42b5-9e31-35b1a22ea31d-0", "path": "07eeba07-b04d-42b5-9e31-35b1a22ea31d-0_0-89-83_20230208203804485.parquet", "prevCommit": "null", "numWrites": 1, "numDeletes": 0, "numUpdateWrites": 0, "numInserts": 1, "totalWriteBytes": 435028, "totalWriteErrors": 0, "tempPath": null, "partitionPath": "", "totalLogRecords": 0, "totalLogFilesCompacted": 0, "totalLogSizeCompacted": 0, "totalUpdatedRecordsCompacted": 0, "totalLogBlocks": 0, "totalCorruptLogBlock": 0, "totalRollbackBlocks": 0, "fileSizeInBytes": 435028, "minEventTime": null, "maxEventTime": null, "runtimeStats": {"totalScanTime": 0, "totalUpsertTime": 0, "totalCreateTime": 197}}]}, "compacted": false, "extraMetadata": {"schema": "{\"type\":\"record\",\"name\":\"test_hudi_mor5_record\",\"namespace\":\"hoodie.test_hudi_mor5\",\"fields\":[{\"name\":\"uuid\",\"type\":\"string\"},{\"name\":\"ts\",\"type\":\"int\"},{\"name\":\"a\",\"type\":\"int\"},{\"name\":\"b\",\"type\":\"string\"},{\"name\":\"c\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"timestamp-micros\"}],\"default\":null}]}"}, "operationType": "UPSERT"}